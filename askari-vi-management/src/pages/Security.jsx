import { useState } from 'react'
import { 
  Shield, 
  Camera, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Eye,
  Lock,
  Unlock,
  Clock,
  MapPin,
  User,
  Phone,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react'

const Security = () => {
  const [activeTab, setActiveTab] = useState('alerts')

  // Dummy security data
  const securityAlerts = [
    {
      id: 1,
      type: 'unauthorized_access',
      severity: 'high',
      location: 'Main Gate',
      description: 'Unauthorized access attempt detected',
      timestamp: '2024-01-15T14:30:00',
      status: 'active',
      assignedTo: 'Security Guard 1'
    },
    {
      id: 2,
      type: 'suspicious_activity',
      severity: 'medium',
      location: 'Parking Area B',
      description: 'Suspicious movement detected in parking area',
      timestamp: '2024-01-15T13:45:00',
      status: 'investigating',
      assignedTo: 'Security Guard 2'
    },
    {
      id: 3,
      type: 'system_malfunction',
      severity: 'low',
      location: 'Camera 15',
      description: 'Camera offline - maintenance required',
      timestamp: '2024-01-15T12:20:00',
      status: 'resolved',
      assignedTo: 'Technical Team'
    }
  ]

  const cameras = [
    {
      id: 1,
      name: 'Main Gate Camera',
      location: 'Main Entrance',
      status: 'online',
      lastUpdate: '2024-01-15T14:35:00',
      recording: true
    },
    {
      id: 2,
      name: 'Parking Area A',
      location: 'North Parking',
      status: 'online',
      lastUpdate: '2024-01-15T14:35:00',
      recording: true
    },
    {
      id: 3,
      name: 'Playground Camera',
      location: 'Children Playground',
      status: 'offline',
      lastUpdate: '2024-01-15T12:20:00',
      recording: false
    },
    {
      id: 4,
      name: 'Back Gate Camera',
      location: 'Service Entrance',
      status: 'online',
      lastUpdate: '2024-01-15T14:35:00',
      recording: true
    }
  ]

  const accessPoints = [
    {
      id: 1,
      name: 'Main Gate',
      type: 'vehicle_pedestrian',
      status: 'open',
      lastActivity: '2024-01-15T14:30:00',
      todayEntries: 145,
      todayExits: 132
    },
    {
      id: 2,
      name: 'Pedestrian Gate',
      type: 'pedestrian',
      status: 'open',
      lastActivity: '2024-01-15T14:25:00',
      todayEntries: 89,
      todayExits: 76
    },
    {
      id: 3,
      name: 'Service Gate',
      type: 'service',
      status: 'locked',
      lastActivity: '2024-01-15T10:15:00',
      todayEntries: 12,
      todayExits: 11
    }
  ]

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'investigating':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'resolved':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      default:
        return <XCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const tabs = [
    { id: 'alerts', label: 'Security Alerts', count: securityAlerts.filter(a => a.status === 'active').length },
    { id: 'cameras', label: 'Surveillance', count: cameras.filter(c => c.status === 'online').length },
    { id: 'access', label: 'Access Control', count: accessPoints.length }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Security Management</h1>
          <p className="text-gray-600">Monitor security alerts, surveillance, and access control</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-red-700">
            <AlertTriangle className="h-5 w-5" />
            <span>Emergency Alert</span>
          </button>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-blue-700">
            <Shield className="h-5 w-5" />
            <span>Security Report</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Security Alerts Tab */}
        {activeTab === 'alerts' && (
          <div className="p-6">
            <div className="space-y-4">
              {securityAlerts.map((alert) => (
                <div key={alert.id} className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      {getStatusIcon(alert.status)}
                      <div>
                        <h3 className="font-medium text-gray-900">{alert.description}</h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {alert.location}
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {new Date(alert.timestamp).toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            {alert.assignedTo}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        alert.severity === 'high' ? 'bg-red-100 text-red-800' :
                        alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {alert.severity}
                      </span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        alert.status === 'active' ? 'bg-red-100 text-red-800' :
                        alert.status === 'investigating' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {alert.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Surveillance Tab */}
        {activeTab === 'cameras' && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cameras.map((camera) => (
                <div key={camera.id} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium text-gray-900">{camera.name}</h3>
                    <div className="flex items-center space-x-2">
                      {camera.status === 'online' ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        camera.status === 'online' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {camera.status}
                      </span>
                    </div>
                  </div>
                  
                  {/* Camera Feed Placeholder */}
                  <div className="bg-black rounded-lg h-32 flex items-center justify-center mb-3">
                    {camera.status === 'online' ? (
                      <div className="text-white text-center">
                        <Camera className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-sm">Live Feed</p>
                      </div>
                    ) : (
                      <div className="text-gray-400 text-center">
                        <XCircle className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-sm">Camera Offline</p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center justify-between">
                      <span>Location:</span>
                      <span>{camera.location}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Recording:</span>
                      <span className={camera.recording ? 'text-green-600' : 'text-red-600'}>
                        {camera.recording ? 'Active' : 'Stopped'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Last Update:</span>
                      <span>{new Date(camera.lastUpdate).toLocaleTimeString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 mt-3">
                    <button className="flex-1 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                      <Eye className="h-4 w-4 inline mr-1" />
                      View
                    </button>
                    <button className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700">
                      {camera.recording ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </button>
                    <button className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700">
                      <RotateCcw className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Access Control Tab */}
        {activeTab === 'access' && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {accessPoints.map((point) => (
                <div key={point.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium text-gray-900">{point.name}</h3>
                    <div className="flex items-center space-x-2">
                      {point.status === 'open' ? (
                        <Unlock className="h-5 w-5 text-green-500" />
                      ) : (
                        <Lock className="h-5 w-5 text-red-500" />
                      )}
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        point.status === 'open' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {point.status}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2 text-sm text-gray-600 mb-4">
                    <div className="flex items-center justify-between">
                      <span>Type:</span>
                      <span className="capitalize">{point.type.replace('_', ' ')}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Last Activity:</span>
                      <span>{new Date(point.lastActivity).toLocaleTimeString()}</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{point.todayEntries}</div>
                      <div className="text-xs text-gray-500">Entries Today</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{point.todayExits}</div>
                      <div className="text-xs text-gray-500">Exits Today</div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button className={`flex-1 px-3 py-2 rounded text-sm font-medium ${
                      point.status === 'open' 
                        ? 'bg-red-600 text-white hover:bg-red-700' 
                        : 'bg-green-600 text-white hover:bg-green-700'
                    }`}>
                      {point.status === 'open' ? 'Lock' : 'Unlock'}
                    </button>
                    <button className="bg-gray-600 text-white px-3 py-2 rounded text-sm hover:bg-gray-700">
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Security
