import { useState, useEffect } from 'react'
import { 
  <PERSON>, 
  UserCheck, 
  Car, 
  Shield, 
  TrendingUp, 
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Activity
} from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'

const Dashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // Dummy data for charts
  const visitorData = [
    { time: '06:00', visitors: 5 },
    { time: '08:00', visitors: 25 },
    { time: '10:00', visitors: 15 },
    { time: '12:00', visitors: 30 },
    { time: '14:00', visitors: 20 },
    { time: '16:00', visitors: 35 },
    { time: '18:00', visitors: 40 },
    { time: '20:00', visitors: 15 },
  ]

  const accessData = [
    { name: 'Authorized', value: 85, color: '#10B981' },
    { name: 'Pending', value: 10, color: '#F59E0B' },
    { name: 'Denied', value: 5, color: '#EF4444' },
  ]

  const recentActivities = [
    { id: 1, type: 'entry', user: 'John Smith', action: 'Visitor entry approved', time: '2 min ago', status: 'success' },
    { id: 2, type: 'exit', user: 'Sarah Johnson', action: 'Resident exit logged', time: '5 min ago', status: 'info' },
    { id: 3, type: 'alert', user: 'Security System', action: 'Unauthorized access attempt', time: '8 min ago', status: 'warning' },
    { id: 4, type: 'entry', user: 'Mike Wilson', action: 'Staff entry logged', time: '12 min ago', status: 'success' },
    { id: 5, type: 'vehicle', user: 'Car ABC-123', action: 'Vehicle registered', time: '15 min ago', status: 'info' },
  ]

  const stats = [
    {
      name: 'Total Residents',
      value: '1,247',
      change: '+12%',
      changeType: 'increase',
      icon: Users,
      color: 'bg-blue-500'
    },
    {
      name: 'Active Visitors',
      value: '89',
      change: '+23%',
      changeType: 'increase',
      icon: UserCheck,
      color: 'bg-green-500'
    },
    {
      name: 'Vehicles Inside',
      value: '456',
      change: '-5%',
      changeType: 'decrease',
      icon: Car,
      color: 'bg-purple-500'
    },
    {
      name: 'Security Alerts',
      value: '3',
      change: '-50%',
      changeType: 'decrease',
      icon: Shield,
      color: 'bg-red-500'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Real-time monitoring and analytics</p>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Current Time</div>
          <div className="text-lg font-semibold text-gray-900">
            {currentTime.toLocaleTimeString()}
          </div>
          <div className="text-sm text-gray-500">
            {currentTime.toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.name} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`${stat.color} rounded-lg p-3`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <div className="flex items-center">
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                    <span className={`ml-2 text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Visitor Traffic Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Visitor Traffic Today</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={visitorData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="visitors" stroke="#3B82F6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Access Control Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Access Control Status</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={accessData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {accessData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
          <div className="flex justify-center space-x-4 mt-4">
            {accessData.map((item) => (
              <div key={item.name} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-2" 
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-gray-600">{item.name}: {item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {recentActivities.map((activity) => (
            <div key={activity.id} className="px-6 py-4 flex items-center justify-between">
              <div className="flex items-center">
                <div className={`p-2 rounded-full ${
                  activity.status === 'success' ? 'bg-green-100' :
                  activity.status === 'warning' ? 'bg-yellow-100' :
                  'bg-blue-100'
                }`}>
                  {activity.status === 'success' && <CheckCircle className="h-4 w-4 text-green-600" />}
                  {activity.status === 'warning' && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                  {activity.status === 'info' && <Activity className="h-4 w-4 text-blue-600" />}
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                  <p className="text-sm text-gray-500">{activity.user}</p>
                </div>
              </div>
              <div className="text-sm text-gray-500 flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                {activity.time}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
