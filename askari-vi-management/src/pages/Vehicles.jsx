import { useState } from 'react'
import { 
  Plus, 
  Search, 
  Car, 
  Truck, 
  Bike,
  Edit,
  Trash2,
  Eye,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

const Vehicles = () => {
  const [activeTab, setActiveTab] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)

  // Dummy vehicle data
  const vehicles = [
    {
      id: 1,
      plateNumber: 'ABC-123',
      type: 'car',
      make: 'Toyota',
      model: 'Corolla',
      year: 2020,
      color: 'White',
      owner: '<PERSON>',
      ownerAddress: 'House 123, Street 5',
      status: 'inside',
      entryTime: '2024-01-15T08:30:00',
      exitTime: null
    },
    {
      id: 2,
      plateNumber: 'XYZ-789',
      type: 'car',
      make: 'Honda',
      model: 'Civic',
      year: 2019,
      color: 'Black',
      owner: '<PERSON><PERSON>',
      ownerAddress: 'House 456, Street 8',
      status: 'outside',
      entryTime: '2024-01-15T07:15:00',
      exitTime: '2024-01-15T17:30:00'
    },
    {
      id: 3,
      plateNumber: 'DEF-456',
      type: 'motorcycle',
      make: 'Honda',
      model: 'CD 70',
      year: 2021,
      color: 'Red',
      owner: 'Muhammad Ali',
      ownerAddress: 'House 789, Street 12',
      status: 'inside',
      entryTime: '2024-01-15T09:45:00',
      exitTime: null
    },
    {
      id: 4,
      plateNumber: 'GHI-789',
      type: 'truck',
      make: 'Suzuki',
      model: 'Bolan',
      year: 2018,
      color: 'Blue',
      owner: 'Service Provider',
      ownerAddress: 'External',
      status: 'inside',
      entryTime: '2024-01-15T14:20:00',
      exitTime: null
    },
    {
      id: 5,
      plateNumber: 'JKL-012',
      type: 'car',
      make: 'Suzuki',
      model: 'Alto',
      year: 2022,
      color: 'Silver',
      owner: 'Sarah Ahmed',
      ownerAddress: 'House 321, Street 3',
      status: 'outside',
      entryTime: '2024-01-15T06:00:00',
      exitTime: '2024-01-15T18:45:00'
    }
  ]

  const filteredVehicles = vehicles.filter(vehicle => {
    const matchesSearch = vehicle.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.model.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesTab = activeTab === 'all' || vehicle.status === activeTab || vehicle.type === activeTab
    
    return matchesSearch && matchesTab
  })

  const getVehicleIcon = (type) => {
    switch (type) {
      case 'car':
        return <Car className="h-6 w-6" />
      case 'motorcycle':
        return <Bike className="h-6 w-6" />
      case 'truck':
        return <Truck className="h-6 w-6" />
      default:
        return <Car className="h-6 w-6" />
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'inside':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'outside':
        return <Clock className="h-5 w-5 text-gray-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'inside':
        return 'bg-green-100 text-green-800'
      case 'outside':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }

  const tabs = [
    { id: 'all', label: 'All Vehicles', count: vehicles.length },
    { id: 'inside', label: 'Inside', count: vehicles.filter(v => v.status === 'inside').length },
    { id: 'outside', label: 'Outside', count: vehicles.filter(v => v.status === 'outside').length },
    { id: 'car', label: 'Cars', count: vehicles.filter(v => v.type === 'car').length },
    { id: 'motorcycle', label: 'Motorcycles', count: vehicles.filter(v => v.type === 'motorcycle').length },
    { id: 'truck', label: 'Trucks', count: vehicles.filter(v => v.type === 'truck').length }
  ]

  const AddVehicleModal = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Register New Vehicle</h3>
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Plate Number</label>
            <input
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter plate number"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Vehicle Type</label>
            <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="car">Car</option>
              <option value="motorcycle">Motorcycle</option>
              <option value="truck">Truck</option>
            </select>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Make</label>
              <input
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Toyota, Honda, etc."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Model</label>
              <input
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Corolla, Civic, etc."
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Year</label>
              <input
                type="number"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="2020"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Color</label>
              <input
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="White, Black, etc."
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Owner</label>
            <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option>Ahmed Hassan - House 123</option>
              <option>Fatima Khan - House 456</option>
              <option>Muhammad Ali - House 789</option>
              <option>Sarah Ahmed - House 321</option>
              <option>Omar Sheikh - House 654</option>
            </select>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowAddModal(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              Register Vehicle
            </button>
          </div>
        </form>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vehicle Management</h1>
          <p className="text-gray-600">Track and manage vehicle entry/exit</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-blue-700"
        >
          <Plus className="h-5 w-5" />
          <span>Register Vehicle</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search vehicles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Vehicle List */}
        <div className="divide-y divide-gray-200">
          {filteredVehicles.map((vehicle) => (
            <div key={vehicle.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    {getVehicleIcon(vehicle.type)}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{vehicle.plateNumber}</h3>
                    <p className="text-sm text-gray-500">
                      {vehicle.year} {vehicle.make} {vehicle.model} - {vehicle.color}
                    </p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <span>Owner: {vehicle.owner}</span>
                      <span>Address: {vehicle.ownerAddress}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(vehicle.status)}
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(vehicle.status)}`}>
                        {vehicle.status}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      Entry: {new Date(vehicle.entryTime).toLocaleString()}
                      {vehicle.exitTime && (
                        <div>Exit: {new Date(vehicle.exitTime).toLocaleString()}</div>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <button className="p-2 text-gray-400 hover:text-blue-600">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add Vehicle Modal */}
      {showAddModal && <AddVehicleModal />}
    </div>
  )
}

export default Vehicles
