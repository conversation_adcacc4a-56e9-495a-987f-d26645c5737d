import { useState } from 'react'
import { 
  Plus, 
  Search, 
  Filter, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  QrCode,
  Phone,
  User,
  Calendar,
  MapPin,
  Eye
} from 'lucide-react'

const Visitors = () => {
  const [activeTab, setActiveTab] = useState('pending')
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)

  // Dummy visitor data
  const visitors = [
    {
      id: 1,
      name: '<PERSON>',
      phone: '+92-300-1234567',
      purpose: 'Business Meeting',
      hostResident: '<PERSON>',
      hostAddress: 'House 123, Street 5',
      arrivalTime: '2024-01-15T14:30:00',
      status: 'pending',
      passCode: 'VIS001',
      vehicleNumber: 'ABC-123'
    },
    {
      id: 2,
      name: '<PERSON>',
      phone: '+92-301-2345678',
      purpose: 'Family Visit',
      hostResident: '<PERSON><PERSON>',
      hostAddress: 'House 456, Street 8',
      arrivalTime: '2024-01-15T16:00:00',
      status: 'approved',
      passCode: 'VIS002',
      vehicleNumber: 'XYZ-789'
    },
    {
      id: 3,
      name: '<PERSON>',
      phone: '+92-302-3456789',
      purpose: 'Delivery',
      hostResident: 'Muhammad Ali',
      hostAddress: 'House 789, Street 12',
      arrivalTime: '2024-01-15T10:15:00',
      status: 'completed',
      passCode: 'VIS003',
      vehicleNumber: 'DEF-456'
    },
    {
      id: 4,
      name: 'Lisa Brown',
      phone: '+92-303-4567890',
      purpose: 'Social Visit',
      hostResident: 'Sarah Ahmed',
      hostAddress: 'House 321, Street 3',
      arrivalTime: '2024-01-15T18:30:00',
      status: 'rejected',
      passCode: 'VIS004',
      vehicleNumber: ''
    },
    {
      id: 5,
      name: 'Robert Johnson',
      phone: '+92-304-5678901',
      purpose: 'Maintenance',
      hostResident: 'Omar Sheikh',
      hostAddress: 'House 654, Street 15',
      arrivalTime: '2024-01-15T09:00:00',
      status: 'approved',
      passCode: 'VIS005',
      vehicleNumber: 'GHI-789'
    }
  ]

  const filteredVisitors = visitors.filter(visitor => {
    const matchesSearch = visitor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         visitor.hostResident.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         visitor.purpose.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesTab = activeTab === 'all' || visitor.status === activeTab
    
    return matchesSearch && matchesTab
  })

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-blue-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const tabs = [
    { id: 'pending', label: 'Pending', count: visitors.filter(v => v.status === 'pending').length },
    { id: 'approved', label: 'Approved', count: visitors.filter(v => v.status === 'approved').length },
    { id: 'completed', label: 'Completed', count: visitors.filter(v => v.status === 'completed').length },
    { id: 'rejected', label: 'Rejected', count: visitors.filter(v => v.status === 'rejected').length },
    { id: 'all', label: 'All', count: visitors.length }
  ]

  const AddVisitorModal = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Pre-Register Visitor</h3>
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Visitor Name</label>
            <input
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter visitor name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Phone Number</label>
            <input
              type="tel"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter phone number"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Purpose of Visit</label>
            <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option>Business Meeting</option>
              <option>Family Visit</option>
              <option>Social Visit</option>
              <option>Delivery</option>
              <option>Maintenance</option>
              <option>Other</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Host Resident</label>
            <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option>Ahmed Hassan - House 123</option>
              <option>Fatima Khan - House 456</option>
              <option>Muhammad Ali - House 789</option>
              <option>Sarah Ahmed - House 321</option>
              <option>Omar Sheikh - House 654</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Expected Arrival</label>
            <input
              type="datetime-local"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Vehicle Number (Optional)</label>
            <input
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter vehicle number"
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowAddModal(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              Register Visitor
            </button>
          </div>
        </form>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Visitor Management</h1>
          <p className="text-gray-600">Pre-register and manage visitor access</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-blue-700"
        >
          <Plus className="h-5 w-5" />
          <span>Pre-Register Visitor</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search visitors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Visitor List */}
        <div className="divide-y divide-gray-200">
          {filteredVisitors.map((visitor) => (
            <div key={visitor.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(visitor.status)}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{visitor.name}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-1" />
                        {visitor.phone}
                      </div>
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        {visitor.hostResident}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(visitor.arrivalTime).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(visitor.status)}`}>
                    {visitor.status}
                  </span>
                  {visitor.status === 'approved' && (
                    <button className="flex items-center space-x-1 text-blue-600 hover:text-blue-800">
                      <QrCode className="h-4 w-4" />
                      <span className="text-sm">Pass: {visitor.passCode}</span>
                    </button>
                  )}
                  <button className="p-2 text-gray-400 hover:text-gray-600">
                    <Eye className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span>Purpose: {visitor.purpose} | Host Address: {visitor.hostAddress}</span>
                  {visitor.vehicleNumber && <span> | Vehicle: {visitor.vehicleNumber}</span>}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add Visitor Modal */}
      {showAddModal && <AddVisitorModal />}
    </div>
  )
}

export default Visitors
