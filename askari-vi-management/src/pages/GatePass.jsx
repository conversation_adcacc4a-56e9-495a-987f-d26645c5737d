import { useState } from 'react'
import { 
  Plus, 
  Search, 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  XCircle,
  Eye,
  Edit,
  Trash2,
  User,
  MapPin,
  Calendar,
  Hash,
  FileText
} from 'lucide-react'

const GatePass = () => {
  const [activeTab, setActiveTab] = useState('pending')
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)

  // Dummy gate pass data
  const gatePasses = [
    {
      id: 1,
      passNumber: 'GP-2024-001',
      type: 'delivery',
      description: 'Furniture delivery for House 123',
      requestedBy: '<PERSON> Hassan',
      destination: 'House 123, Street 5',
      vendor: 'ABC Furniture Store',
      vendorContact: '+92-300-1111111',
      vehicleNumber: 'DEL-123',
      expectedDate: '2024-01-15',
      expectedTime: '14:00',
      status: 'pending',
      items: ['Sofa Set', 'Dining Table', 'Chairs'],
      createdAt: '2024-01-15T10:30:00'
    },
    {
      id: 2,
      passNumber: 'GP-2024-002',
      type: 'material',
      description: 'Construction materials for renovation',
      requestedBy: 'Fatima Khan',
      destination: 'House 456, Street 8',
      vendor: 'XYZ Construction Supplies',
      vendorContact: '+92-301-2222222',
      vehicleNumber: 'MAT-456',
      expectedDate: '2024-01-15',
      expectedTime: '09:00',
      status: 'approved',
      items: ['Cement bags', 'Steel rods', 'Bricks'],
      createdAt: '2024-01-14T16:20:00'
    },
    {
      id: 3,
      passNumber: 'GP-2024-003',
      type: 'pickup',
      description: 'Old furniture pickup',
      requestedBy: 'Muhammad Ali',
      destination: 'House 789, Street 12',
      vendor: 'Charity Organization',
      vendorContact: '+92-302-3333333',
      vehicleNumber: 'CHR-789',
      expectedDate: '2024-01-15',
      expectedTime: '16:00',
      status: 'completed',
      items: ['Old sofa', 'Broken chairs', 'Used appliances'],
      createdAt: '2024-01-14T14:15:00'
    },
    {
      id: 4,
      passNumber: 'GP-2024-004',
      type: 'service',
      description: 'AC maintenance service',
      requestedBy: 'Sarah Ahmed',
      destination: 'House 321, Street 3',
      vendor: 'Cool Air Services',
      vendorContact: '+92-303-4444444',
      vehicleNumber: 'SRV-321',
      expectedDate: '2024-01-16',
      expectedTime: '11:00',
      status: 'rejected',
      items: ['AC repair tools', 'Spare parts'],
      createdAt: '2024-01-15T08:45:00'
    }
  ]

  const filteredPasses = gatePasses.filter(pass => {
    const matchesSearch = pass.passNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pass.requestedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pass.vendor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pass.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesTab = activeTab === 'all' || pass.status === activeTab
    
    return matchesSearch && matchesTab
  })

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-blue-500" />
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type) => {
    switch (type) {
      case 'delivery':
        return <Package className="h-5 w-5 text-blue-500" />
      case 'material':
        return <Truck className="h-5 w-5 text-green-500" />
      case 'pickup':
        return <Package className="h-5 w-5 text-purple-500" />
      case 'service':
        return <User className="h-5 w-5 text-orange-500" />
      default:
        return <Package className="h-5 w-5 text-gray-500" />
    }
  }

  const tabs = [
    { id: 'pending', label: 'Pending', count: gatePasses.filter(p => p.status === 'pending').length },
    { id: 'approved', label: 'Approved', count: gatePasses.filter(p => p.status === 'approved').length },
    { id: 'completed', label: 'Completed', count: gatePasses.filter(p => p.status === 'completed').length },
    { id: 'rejected', label: 'Rejected', count: gatePasses.filter(p => p.status === 'rejected').length },
    { id: 'all', label: 'All', count: gatePasses.length }
  ]

  const AddGatePassModal = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Create Gate Pass</h3>
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Pass Type</label>
            <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="delivery">Delivery</option>
              <option value="material">Material/Construction</option>
              <option value="pickup">Pickup</option>
              <option value="service">Service</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              rows="3"
              placeholder="Describe the purpose of this gate pass"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Requested By</label>
            <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option>Ahmed Hassan - House 123</option>
              <option>Fatima Khan - House 456</option>
              <option>Muhammad Ali - House 789</option>
              <option>Sarah Ahmed - House 321</option>
              <option>Omar Sheikh - House 654</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Vendor/Company Name</label>
            <input
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter vendor or company name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Vendor Contact</label>
            <input
              type="tel"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter contact number"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Vehicle Number</label>
            <input
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter vehicle number"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Expected Date</label>
              <input
                type="date"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Expected Time</label>
              <input
                type="time"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Items/Materials</label>
            <textarea
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              rows="3"
              placeholder="List items or materials (one per line)"
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowAddModal(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              Create Gate Pass
            </button>
          </div>
        </form>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gate Pass Management</h1>
          <p className="text-gray-600">Manage material delivery and cargo gate passes</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-blue-700"
        >
          <Plus className="h-5 w-5" />
          <span>Create Gate Pass</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search gate passes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Gate Pass List */}
        <div className="divide-y divide-gray-200">
          {filteredPasses.map((pass) => (
            <div key={pass.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    {getTypeIcon(pass.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">{pass.passNumber}</h3>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        pass.type === 'delivery' ? 'bg-blue-100 text-blue-800' :
                        pass.type === 'material' ? 'bg-green-100 text-green-800' :
                        pass.type === 'pickup' ? 'bg-purple-100 text-purple-800' :
                        'bg-orange-100 text-orange-800'
                      }`}>
                        {pass.type}
                      </span>
                    </div>
                    <p className="text-gray-700 mb-2">{pass.description}</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                      <div className="space-y-1">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2" />
                          <span>Requested by: {pass.requestedBy}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2" />
                          <span>Destination: {pass.destination}</span>
                        </div>
                        <div className="flex items-center">
                          <Truck className="h-4 w-4 mr-2" />
                          <span>Vendor: {pass.vendor}</span>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          <span>Expected: {pass.expectedDate} at {pass.expectedTime}</span>
                        </div>
                        <div className="flex items-center">
                          <Hash className="h-4 w-4 mr-2" />
                          <span>Vehicle: {pass.vehicleNumber}</span>
                        </div>
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2" />
                          <span>Items: {pass.items.join(', ')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="flex items-center space-x-2 mb-1">
                      {getStatusIcon(pass.status)}
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(pass.status)}`}>
                        {pass.status}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      Created: {new Date(pass.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <button className="p-2 text-gray-400 hover:text-blue-600">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add Gate Pass Modal */}
      {showAddModal && <AddGatePassModal />}
    </div>
  )
}

export default GatePass
