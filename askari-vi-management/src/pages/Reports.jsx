import { useState } from 'react'
import { 
  BarChart3, 
  Download, 
  Calendar, 
  Filter,
  Users,
  UserCheck,
  Car,
  Package,
  TrendingUp,
  TrendingDown,
  Activity
} from 'lucide-react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON><PERSON>hart, Pie, Cell } from 'recharts'

const Reports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week')
  const [selectedReport, setSelectedReport] = useState('overview')

  // Dummy data for reports
  const weeklyData = [
    { day: 'Mon', residents: 45, visitors: 23, vehicles: 67, gatePasses: 8 },
    { day: 'Tue', residents: 52, visitors: 31, vehicles: 73, gatePasses: 12 },
    { day: 'Wed', residents: 48, visitors: 28, vehicles: 69, gatePasses: 6 },
    { day: 'Thu', residents: 61, visitors: 35, vehicles: 82, gatePasses: 15 },
    { day: 'Fri', residents: 55, visitors: 42, vehicles: 78, gatePasses: 9 },
    { day: 'Sat', residents: 38, visitors: 18, vehicles: 54, gatePasses: 4 },
    { day: 'Sun', residents: 42, visitors: 25, vehicles: 61, gatePasses: 7 }
  ]

  const monthlyTrends = [
    { month: 'Jan', total: 1250 },
    { month: 'Feb', total: 1180 },
    { month: 'Mar', total: 1320 },
    { month: 'Apr', total: 1280 },
    { month: 'May', total: 1450 },
    { month: 'Jun', total: 1380 }
  ]

  const visitorTypes = [
    { name: 'Family', value: 45, color: '#3B82F6' },
    { name: 'Business', value: 25, color: '#10B981' },
    { name: 'Service', value: 20, color: '#F59E0B' },
    { name: 'Delivery', value: 10, color: '#EF4444' }
  ]

  const reportStats = [
    {
      title: 'Total Entries Today',
      value: '234',
      change: '+12%',
      changeType: 'increase',
      icon: Activity,
      color: 'text-blue-600'
    },
    {
      title: 'Active Residents',
      value: '1,247',
      change: '+3%',
      changeType: 'increase',
      icon: Users,
      color: 'text-green-600'
    },
    {
      title: 'Visitor Traffic',
      value: '89',
      change: '-5%',
      changeType: 'decrease',
      icon: UserCheck,
      color: 'text-purple-600'
    },
    {
      title: 'Gate Passes',
      value: '23',
      change: '+18%',
      changeType: 'increase',
      icon: Package,
      color: 'text-orange-600'
    }
  ]

  const reportTypes = [
    { id: 'overview', label: 'Overview Report' },
    { id: 'residents', label: 'Residents Report' },
    { id: 'visitors', label: 'Visitors Report' },
    { id: 'vehicles', label: 'Vehicles Report' },
    { id: 'security', label: 'Security Report' },
    { id: 'gatepasses', label: 'Gate Passes Report' }
  ]

  const periods = [
    { id: 'today', label: 'Today' },
    { id: 'week', label: 'This Week' },
    { id: 'month', label: 'This Month' },
    { id: 'quarter', label: 'This Quarter' },
    { id: 'year', label: 'This Year' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600">Generate comprehensive reports and analytics</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-green-700">
            <Download className="h-5 w-5" />
            <span>Export PDF</span>
          </button>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-blue-700">
            <Download className="h-5 w-5" />
            <span>Export Excel</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-5 w-5 text-gray-400" />
            <select
              value={selectedReport}
              onChange={(e) => setSelectedReport(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {reportTypes.map((type) => (
                <option key={type.id} value={type.id}>{type.label}</option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-gray-400" />
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {periods.map((period) => (
                <option key={period.id} value={period.id}>{period.label}</option>
              ))}
            </select>
          </div>
          <div className="flex space-x-2">
            <input
              type="date"
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <span className="flex items-center text-gray-500">to</span>
            <input
              type="date"
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {reportStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <div className="flex items-center mt-2">
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                    <span className={`ml-2 text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.changeType === 'increase' ? (
                        <TrendingUp className="h-4 w-4 inline mr-1" />
                      ) : (
                        <TrendingDown className="h-4 w-4 inline mr-1" />
                      )}
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg bg-gray-100`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Activity Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Weekly Activity Overview</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={weeklyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="residents" fill="#3B82F6" name="Residents" />
              <Bar dataKey="visitors" fill="#10B981" name="Visitors" />
              <Bar dataKey="vehicles" fill="#F59E0B" name="Vehicles" />
              <Bar dataKey="gatePasses" fill="#EF4444" name="Gate Passes" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Monthly Trends */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={monthlyTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="total" stroke="#3B82F6" strokeWidth={3} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Visitor Types and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Visitor Types Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Visitor Types Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={visitorTypes}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {visitorTypes.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
          <div className="flex justify-center space-x-4 mt-4">
            {visitorTypes.map((item) => (
              <div key={item.name} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-2" 
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-gray-600">{item.name}: {item.value}%</span>
              </div>
            ))}
          </div>
        </div>

        {/* Summary Statistics */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Summary Statistics</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Average Daily Visitors</span>
              <span className="text-lg font-semibold text-gray-900">32</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Peak Hour Traffic</span>
              <span className="text-lg font-semibold text-gray-900">6:00 PM</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Security Incidents</span>
              <span className="text-lg font-semibold text-red-600">3</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Gate Pass Approval Rate</span>
              <span className="text-lg font-semibold text-green-600">94%</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Average Visit Duration</span>
              <span className="text-lg font-semibold text-gray-900">2.5 hrs</span>
            </div>
          </div>
        </div>
      </div>

      {/* Report Generation */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Generate Custom Report</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
            <select className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option>Comprehensive Report</option>
              <option>Visitor Summary</option>
              <option>Security Report</option>
              <option>Vehicle Report</option>
              <option>Gate Pass Report</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Format</label>
            <select className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option>PDF</option>
              <option>Excel</option>
              <option>CSV</option>
            </select>
          </div>
          <div className="flex items-end">
            <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Generate Report</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Reports
