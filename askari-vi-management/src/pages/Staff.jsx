import { useState } from 'react'
import { 
  Plus, 
  Search, 
  Filter,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Edit,
  Trash2,
  Eye,
  Phone,
  Mail,
  MapPin,
  Calendar,
  User
} from 'lucide-react'

const Staff = () => {
  const [activeTab, setActiveTab] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)

  // Dummy staff data
  const staff = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>',
      type: 'maid',
      phone: '+92-300-1111111',
      cnic: '12345-6789012-3',
      employer: '<PERSON>',
      employerAddress: 'House 123, Street 5',
      status: 'inside',
      entryTime: '2024-01-15T08:00:00',
      exitTime: null,
      workingDays: 'Mon-Fri',
      salary: 15000
    },
    {
      id: 2,
      name: '<PERSON>',
      type: 'gardener',
      phone: '+92-301-2222222',
      cnic: '23456-7890123-4',
      employer: 'Community',
      employerAddress: 'Common Areas',
      status: 'outside',
      entryTime: '2024-01-15T07:00:00',
      exitTime: '2024-01-15T15:00:00',
      workingDays: 'Mon-Sat',
      salary: 25000
    },
    {
      id: 3,
      name: 'Fatima Khatoon',
      type: 'maid',
      phone: '+92-302-3333333',
      cnic: '34567-8901234-5',
      employer: 'Fatima Khan',
      employerAddress: 'House 456, Street 8',
      status: 'inside',
      entryTime: '2024-01-15T09:30:00',
      exitTime: null,
      workingDays: 'Tue-Sat',
      salary: 18000
    },
    {
      id: 4,
      name: 'Ali Hassan',
      type: 'security',
      phone: '+92-303-4444444',
      cnic: '45678-9012345-6',
      employer: 'Community',
      employerAddress: 'Security Office',
      status: 'inside',
      entryTime: '2024-01-15T06:00:00',
      exitTime: null,
      workingDays: 'Daily',
      salary: 35000
    },
    {
      id: 5,
      name: 'Nazia Begum',
      type: 'cook',
      phone: '+92-304-5555555',
      cnic: '56789-0123456-7',
      employer: 'Sarah Ahmed',
      employerAddress: 'House 321, Street 3',
      status: 'outside',
      entryTime: '2024-01-15T10:00:00',
      exitTime: '2024-01-15T14:00:00',
      workingDays: 'Mon-Fri',
      salary: 20000
    }
  ]

  const filteredStaff = staff.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.employer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.type.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesTab = activeTab === 'all' || member.status === activeTab || member.type === activeTab
    
    return matchesSearch && matchesTab
  })

  const getStatusIcon = (status) => {
    switch (status) {
      case 'inside':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'outside':
        return <Clock className="h-5 w-5 text-gray-500" />
      default:
        return <XCircle className="h-5 w-5 text-red-500" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'inside':
        return 'bg-green-100 text-green-800'
      case 'outside':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-red-100 text-red-800'
    }
  }

  const getTypeColor = (type) => {
    switch (type) {
      case 'maid':
        return 'bg-blue-100 text-blue-800'
      case 'gardener':
        return 'bg-green-100 text-green-800'
      case 'security':
        return 'bg-red-100 text-red-800'
      case 'cook':
        return 'bg-purple-100 text-purple-800'
      case 'driver':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const tabs = [
    { id: 'all', label: 'All Staff', count: staff.length },
    { id: 'inside', label: 'Inside', count: staff.filter(s => s.status === 'inside').length },
    { id: 'outside', label: 'Outside', count: staff.filter(s => s.status === 'outside').length },
    { id: 'maid', label: 'Maids', count: staff.filter(s => s.type === 'maid').length },
    { id: 'gardener', label: 'Gardeners', count: staff.filter(s => s.type === 'gardener').length },
    { id: 'security', label: 'Security', count: staff.filter(s => s.type === 'security').length },
    { id: 'cook', label: 'Cooks', count: staff.filter(s => s.type === 'cook').length }
  ]

  const AddStaffModal = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Register New Staff Member</h3>
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Full Name</label>
            <input
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter full name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Staff Type</label>
            <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="maid">Maid</option>
              <option value="gardener">Gardener</option>
              <option value="security">Security</option>
              <option value="cook">Cook</option>
              <option value="driver">Driver</option>
              <option value="cleaner">Cleaner</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Phone Number</label>
            <input
              type="tel"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter phone number"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">CNIC Number</label>
            <input
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="12345-6789012-3"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Employer</label>
            <select className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="community">Community</option>
              <option>Ahmed Hassan - House 123</option>
              <option>Fatima Khan - House 456</option>
              <option>Muhammad Ali - House 789</option>
              <option>Sarah Ahmed - House 321</option>
              <option>Omar Sheikh - House 654</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Working Days</label>
            <input
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Mon-Fri, Daily, etc."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Monthly Salary (PKR)</label>
            <input
              type="number"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="25000"
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowAddModal(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              Register Staff
            </button>
          </div>
        </form>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Staff Management</h1>
          <p className="text-gray-600">Manage domestic staff and service providers</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-blue-700"
        >
          <Plus className="h-5 w-5" />
          <span>Register Staff</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search staff members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Staff List */}
        <div className="divide-y divide-gray-200">
          {filteredStaff.map((member) => (
            <div key={member.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <User className="h-6 w-6 text-gray-600" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-medium text-gray-900">{member.name}</h3>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(member.type)}`}>
                        {member.type}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-1" />
                        {member.phone}
                      </div>
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        CNIC: {member.cnic}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {member.employer} - {member.employerAddress}
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        Working Days: {member.workingDays}
                      </div>
                      <span>Salary: PKR {member.salary.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(member.status)}
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(member.status)}`}>
                        {member.status}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      Entry: {new Date(member.entryTime).toLocaleString()}
                      {member.exitTime && (
                        <div>Exit: {new Date(member.exitTime).toLocaleString()}</div>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <button className="p-2 text-gray-400 hover:text-blue-600">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add Staff Modal */}
      {showAddModal && <AddStaffModal />}
    </div>
  )
}

export default Staff
