/**
 * @fileoverview `ConfigDependency` class.
 *
 * `ConfigDependency` class expresses a loaded parser or plugin.
 *
 * If the parser or plugin was loaded successfully, it has `definition` property
 * and `filePath` property. Otherwise, it has `error` property.
 *
 * When `JSON.stringify()` converted a `ConfigDependency` object to a JSON, it
 * omits `definition` property.
 *
 * `ConfigArrayFactory` creates `ConfigDependency` objects when it loads parsers
 * or plugins.
 *
 * <AUTHOR> <https://github.com/mysticatea>
 */

import util from "node:util";

/**
 * The class is to store parsers or plugins.
 * This class hides the loaded object from `JSON.stringify()` and `console.log`.
 * @template T
 */
class ConfigDependency {

    /**
     * Initialize this instance.
     * @param {Object} data The dependency data.
     * @param {T} [data.definition] The dependency if the loading succeeded.
     * @param {T} [data.original] The original, non-normalized dependency if the loading succeeded.
     * @param {Error} [data.error] The error object if the loading failed.
     * @param {string} [data.filePath] The actual path to the dependency if the loading succeeded.
     * @param {string} data.id The ID of this dependency.
     * @param {string} data.importerName The name of the config file which loads this dependency.
     * @param {string} data.importerPath The path to the config file which loads this dependency.
     */
    constructor({
        definition = null,
        original = null,
        error = null,
        filePath = null,
        id,
        importerName,
        importerPath
    }) {

        /**
         * The loaded dependency if the loading succeeded.
         * @type {T|null}
         */
        this.definition = definition;

        /**
         * The original dependency as loaded directly from disk if the loading succeeded.
         * @type {T|null}
         */
        this.original = original;

        /**
         * The error object if the loading failed.
         * @type {Error|null}
         */
        this.error = error;

        /**
         * The loaded dependency if the loading succeeded.
         * @type {string|null}
         */
        this.filePath = filePath;

        /**
         * The ID of this dependency.
         * @type {string}
         */
        this.id = id;

        /**
         * The name of the config file which loads this dependency.
         * @type {string}
         */
        this.importerName = importerName;

        /**
         * The path to the config file which loads this dependency.
         * @type {string}
         */
        this.importerPath = importerPath;
    }

    /**
     * Converts this instance to a JSON compatible object.
     * @returns {Object} a JSON compatible object.
     */
    toJSON() {
        const obj = this[util.inspect.custom]();

        // Display `error.message` (`Error#message` is unenumerable).
        if (obj.error instanceof Error) {
            obj.error = { ...obj.error, message: obj.error.message };
        }

        return obj;
    }

    /**
     * Custom inspect method for Node.js `console.log()`.
     * @returns {Object} an object to display by `console.log()`.
     */
    [util.inspect.custom]() {
        const {
            definition: _ignore1, // eslint-disable-line no-unused-vars -- needed to make `obj` correct
            original: _ignore2, // eslint-disable-line no-unused-vars -- needed to make `obj` correct
            ...obj
        } = this;

        return obj;
    }
}

/** @typedef {ConfigDependency<import("../../shared/types").Parser>} DependentParser */
/** @typedef {ConfigDependency<import("../../shared/types").Plugin>} DependentPlugin */

export { ConfigDependency };
