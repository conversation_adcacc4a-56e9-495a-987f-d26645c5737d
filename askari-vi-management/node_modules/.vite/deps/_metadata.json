{"hash": "2699b52a", "configHash": "34a7310a", "lockfileHash": "d008e536", "browserHash": "ce36d2c5", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e1e35e45", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "deb23570", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d643c752", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b654a521", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "166ead10", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "2d17e860", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "aea2658a", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "b0e4651a", "needsInterop": false}}, "chunks": {"chunk-I5HANLAN": {"file": "chunk-I5HANLAN.js"}, "chunk-X4QARNC5": {"file": "chunk-X4QARNC5.js"}}}