{"hash": "f9c321d5", "configHash": "34a7310a", "lockfileHash": "ac707b15", "browserHash": "59e8b0a8", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "7cb0cf11", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "12b28a44", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "6cef9cfd", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "8bcc0cc4", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "ec15fc48", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d1de3878", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "8dcf21d7", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "cdbfa22a", "needsInterop": false}}, "chunks": {"chunk-I5HANLAN": {"file": "chunk-I5HANLAN.js"}, "chunk-X4QARNC5": {"file": "chunk-X4QARNC5.js"}}}