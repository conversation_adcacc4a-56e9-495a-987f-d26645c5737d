{"hash": "fe10d946", "configHash": "34a7310a", "lockfileHash": "647dccfd", "browserHash": "d653ba08", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "933af3a6", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "693fb63c", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3d08d788", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "95663eaa", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "edb1f635", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "954441b1", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "8755779e", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "d1752c78", "needsInterop": false}}, "chunks": {"chunk-I5HANLAN": {"file": "chunk-I5HANLAN.js"}, "chunk-X4QARNC5": {"file": "chunk-X4QARNC5.js"}}}