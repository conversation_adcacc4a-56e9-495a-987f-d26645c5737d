{"version": 3, "file": "pack.js", "sourceRoot": "", "sources": ["../../src/pack.ts"], "names": [], "mappings": ";AAAA,gCAAgC;AAChC,qEAAqE;AACrE,+BAA+B;AAC/B,yDAAyD;AACzD,8CAA8C;AAC9C,+DAA+D;AAC/D,oCAAoC;AACpC,uEAAuE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvE,4CAAmC;AACnC,qDAIyB;AAEzB,MAAa,OAAO;IAClB,IAAI,CAAQ;IACZ,QAAQ,CAAQ;IAChB,KAAK,CAA6B;IAClC,IAAI,CAAQ;IACZ,OAAO,CAAW;IAClB,OAAO,GAAY,KAAK,CAAA;IACxB,MAAM,GAAY,KAAK,CAAA;IACvB,KAAK,GAAY,KAAK,CAAA;IACtB,YAAY,IAAY,EAAE,QAAgB;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAA;QACxB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC1B,CAAC;CACF;AAbD,0BAaC;AAED,uCAAmC;AACnC,+CAAgC;AAChC,qCAAiC;AACjC,mDAA2C;AAC3C,qDAKyB;AAEzB,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;AAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,CAAA;AACzC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAA;AACjD,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AAEjC,gDAAuB;AACvB,2EAAkE;AAGlE,MAAa,IACX,SAAQ,mBAAuD;IAG/D,GAAG,CAAY;IACf,GAAG,CAAQ;IACX,WAAW,CAAS;IACpB,aAAa,CAAS;IACtB,MAAM,CAAS;IACf,KAAK,CAAS;IACd,MAAM,CAAQ;IACd,SAAS,CAA6C;IACtD,SAAS,CAA6C;IACtD,IAAI,CAAQ;IACZ,QAAQ,CAAS;IACjB,GAAG,CAAsD;IACzD,YAAY,CAAgD;IAC5D,YAAY,CAAS;IACrB,MAAM,CAAS;IACf,OAAO,CAAS;IAChB,KAAK,CAAO;IACZ,MAAM,CAA0C;IAChD,IAAI,CAAS;IAEb,CAAC,eAAe,CAAC,CAA2C;IAC5D,YAAY,CAA+B;IAC3C,2DAA2D;IAC3D,mEAAmE;IACnE,mEAAmE;IACnE,qEAAqE;IACrE,wEAAwE;IACxE,sEAAsE;IACtE,qEAAqE;IACrE,wDAAwD;IACxD,CAAC,KAAK,CAAC,CAAmB;IAC1B,CAAC,IAAI,CAAC,GAAW,CAAC,CAAC;IACnB,CAAC,UAAU,CAAC,GAAY,KAAK,CAAC;IAC9B,CAAC,KAAK,CAAC,GAAY,KAAK,CAAA;IAExB,YAAY,MAAkB,EAAE;QAC9B,YAAY;QACZ,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAA;QAC1B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAA;QAClC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,aAAa,CAAA;QACxC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,MAAM,GAAG,IAAA,gDAAoB,EAAC,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;QACpD,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAI,GAAG,EAAE,CAAA;QAC3C,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAI,GAAG,EAAE,CAAA;QAC3C,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,IAAI,IAAI,GAAG,EAAE,CAAA;QACjD,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAA;QAEpC,IAAI,CAAC,eAAe,CAAC,GAAG,2BAAU,CAAA;QAClC,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACrC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAA;QAE9B,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACvC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvE,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAA;YAClE,CAAC;YACD,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;gBACb,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACjC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAA;gBACf,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBAC1B,CAAC;gBACD,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YACpC,CAAC;YACD,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACf,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;oBACnC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAA;gBACjB,CAAC;gBACD,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAChD,CAAC;YACD,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;gBACb,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACjC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAA;gBACf,CAAC;gBACD,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC5C,CAAC;YACD,oBAAoB;YACpB,IAAI,CAAC,IAAI,CAAC,GAAG;gBAAE,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;YAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;YACpB,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,KAA0B,CAAC,CAAC,CAAA;YAChE,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;YAChC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;YACtC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAA;QACvC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;QACjC,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,YAAY,CAAA;QACtC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAA;QAC1B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAA;QAC5B,IAAI,GAAG,CAAC,KAAK;YAAE,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;QAErC,IAAI,CAAC,MAAM;YACT,OAAO,GAAG,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAA;QAE5D,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,iBAAO,EAAW,CAAA;QACpC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACd,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACjC,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;QACxB,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAA;IACrB,CAAC;IAED,CAAC,KAAK,CAAC,CAAC,KAAa;QACnB,OAAO,KAAK,CAAC,KAAK,CAAC,KAA0B,CAAC,CAAA;IAChD,CAAC;IAED,GAAG,CAAC,IAAwB;QAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;IASD,GAAG,CACD,IAAwC,EACxC,QAA2C,EAC3C,EAAe;QAEf,qBAAqB;QACrB,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,EAAE,GAAG,IAAI,CAAA;YACT,IAAI,GAAG,SAAS,CAAA;QAClB,CAAC;QACD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,EAAE,GAAG,QAAQ,CAAA;YACb,QAAQ,GAAG,SAAS,CAAA;QACtB,CAAC;QACD,oBAAoB;QACpB,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAChB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;QACf,oBAAoB;QACpB,IAAI,EAAE;YAAE,EAAE,EAAE,CAAA;QACZ,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK,CAAC,IAAwB;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;QACpC,CAAC;QAED,IAAI,IAAI,YAAY,yBAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAA;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAA;QACxB,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAED,CAAC,WAAW,CAAC,CAAC,CAAY;QACxB,MAAM,QAAQ,GAAG,IAAA,gDAAoB,EACnC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAC/B,CAAA;QACD,mDAAmD;QACnD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YAC5B,CAAC,CAAC,MAAM,EAAE,CAAA;QACZ,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YACzC,GAAG,CAAC,KAAK,GAAG,IAAI,8BAAa,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YACrD,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;IACjB,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,CAAS;QACpB,MAAM,QAAQ,GAAG,IAAA,gDAAoB,EAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;QAChE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAA;QAC1C,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;IACjB,CAAC;IAED,CAAC,IAAI,CAAC,CAAC,GAAY;QACjB,GAAG,CAAC,OAAO,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAA;QAC3C,YAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,GAAG,CAAC,OAAO,GAAG,KAAK,CAAA;YACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,IAAI,EAAE,EAAE,CAAC;gBACP,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YACxB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YACzB,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,CAAC,MAAM,CAAC,CAAC,GAAY,EAAE,IAAW;QAChC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACtC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAA;QAEf,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YACjC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAA;QACnB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;IACjB,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,GAAY;QACpB,GAAG,CAAC,OAAO,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,YAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE;YACvC,GAAG,CAAC,OAAO,GAAG,KAAK,CAAA;YACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,IAAI,EAAE,EAAE,CAAC;gBACP,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YAC/B,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QAC/B,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,CAAC,SAAS,CAAC,CAAC,GAAY,EAAE,OAAiB;QACzC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAC5C,GAAG,CAAC,OAAO,GAAG,OAAO,CAAA;QACrB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;IACjB,CAAC;IAED,CAAC,OAAO,CAAC;QACP,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACrB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAA;QACvB,KACE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EACxB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAC7B,CAAC,GAAG,CAAC,CAAC,IAAI,EACV,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;YACzB,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAA;gBAChB,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;gBACzB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAA;YACZ,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;QAExB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3D,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,KAAK,CAAC,GAAwB,CAAC,CAAA;gBACrC,KAAK,CAAC,GAAG,EAAE,CAAA;YACb,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAO,CAAC;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;IAClE,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,IAAa;QACrB,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAA;QACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;IACjB,CAAC;IAED,CAAC,UAAU,CAAC,CAAC,GAAY;QACvB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,OAAM;QACR,CAAC;QAED,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;YACd,IAAI,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YACjB,CAAC;YACD,OAAM;QACR,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC3C,IAAI,EAAE,EAAE,CAAC;gBACP,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;YACvB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YACjB,CAAC;QACH,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAM;QACR,CAAC;QAED,gBAAgB;QAChB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,OAAM;QACR,CAAC;QAED,IACE,CAAC,IAAI,CAAC,YAAY;YAClB,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YACtB,CAAC,GAAG,CAAC,OAAO,EACZ,CAAC;YACD,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAC9C,IAAI,EAAE,EAAE,CAAC;gBACP,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;YAC1B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;YACpB,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAM;YACR,CAAC;QACH,CAAC;QAED,mEAAmE;QACnE,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAA;QAC5B,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,MAAM,GAAG,IAAI,CAAA;YACjB,OAAM;QACR,CAAC;QAED,IAAI,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;QACjB,CAAC;IACH,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,GAAY;QACrB,OAAO;YACL,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;YACvD,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,YAAY,EAAE,IAAI,CAAC,YAAY;SAChC,CAAA;IACH,CAAC;IAED,CAAC,KAAK,CAAC,CAAC,GAAY;QAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,CAAC;YACH,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,CACjC,GAAG,CAAC,IAAI,EACR,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CACpB,CAAA;YACD,OAAO,CAAC;iBACL,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;iBACnC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;IAED,CAAC,OAAO,CAAC;QACP,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAA;QAC9B,CAAC;IACH,CAAC;IAED,+DAA+D;IAC/D,CAAC,IAAI,CAAC,CAAC,GAAY;QACjB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAA;QAEhB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAA;gBAClB,MAAM,IAAI,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;gBACrD,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;YAChC,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAA;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,qBAAqB;QACrB,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC1D,oBAAoB;QAEpB,IAAI,GAAG,EAAE,CAAC;YACR,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtB,MAAM,CAAC,KAAK,EAAE,CAAA;gBAChB,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAA0B,CAAC,EAAE,CAAC;oBAC7C,MAAM,CAAC,KAAK,EAAE,CAAA;gBAChB,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAA;QAClB,CAAC;QACD,OAAO,KAAK,CAAC,KAAK,EAAE,CAAA;IACtB,CAAC;IACD,IAAI,CACF,IAAY,EACZ,OAAuB,EACvB,OAAiB,EAAE;QAEnB,IAAA,2BAAU,EAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;IACvC,CAAC;CACF;AAraD,oBAqaC;AAED,MAAa,QAAS,SAAQ,IAAI;IAChC,IAAI,GAAS,IAAI,CAAA;IACjB,YAAY,GAAe;QACzB,KAAK,CAAC,GAAG,CAAC,CAAA;QACV,IAAI,CAAC,eAAe,CAAC,GAAG,+BAAc,CAAA;IACxC,CAAC;IAED,2CAA2C;IAC3C,KAAK,KAAI,CAAC;IACV,MAAM,KAAI,CAAC;IAEX,CAAC,IAAI,CAAC,CAAC,GAAY;QACjB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAA;QACnD,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,YAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC3C,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,GAAY;QACpB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;IACpD,CAAC;IAED,gCAAgC;IAChC,CAAC,IAAI,CAAC,CAAC,GAAY;QACjB,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAA;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QAEpB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAA;gBAClB,MAAM,IAAI,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;gBACrD,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,CAAA;YAChC,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC1D,oBAAoB;QAEpB,IAAI,GAAG,EAAE,CAAC;YACR,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;gBACxB,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAClB,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;gBACxB,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAA;YACrB,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;CACF;AA/CD,4BA+CC", "sourcesContent": ["// A readable tar stream creator\n// Technically, this is a transform stream that you write paths into,\n// and tar format comes out of.\n// The `add()` method is like `write()` but returns this,\n// and end() return `this` as well, so you can\n// do `new Pack(opt).add('files').add('dir').end().pipe(output)\n// You could also do something like:\n// streamOfPaths().pipe(new Pack()).pipe(new fs.WriteStream('out.tar'))\n\nimport fs, { type Stats } from 'fs'\nimport {\n  WriteEntry,\n  WriteEntrySync,\n  WriteEntryTar,\n} from './write-entry.js'\n\nexport class PackJob {\n  path: string\n  absolute: string\n  entry?: WriteEntry | WriteEntryTar\n  stat?: Stats\n  readdir?: string[]\n  pending: boolean = false\n  ignore: boolean = false\n  piped: boolean = false\n  constructor(path: string, absolute: string) {\n    this.path = path || './'\n    this.absolute = absolute\n  }\n}\n\nimport { Minipass } from 'minipass'\nimport * as zlib from 'minizlib'\nimport { Yallist } from 'yallist'\nimport { ReadEntry } from './read-entry.js'\nimport {\n  WarnEvent,\n  warnMethod,\n  type WarnData,\n  type Warner,\n} from './warn-method.js'\n\nconst EOF = Buffer.alloc(1024)\nconst ONSTAT = Symbol('onStat')\nconst ENDED = Symbol('ended')\nconst QUEUE = Symbol('queue')\nconst CURRENT = Symbol('current')\nconst PROCESS = Symbol('process')\nconst PROCESSING = Symbol('processing')\nconst PROCESSJOB = Symbol('processJob')\nconst JOBS = Symbol('jobs')\nconst JOBDONE = Symbol('jobDone')\nconst ADDFSENTRY = Symbol('addFSEntry')\nconst ADDTARENTRY = Symbol('addTarEntry')\nconst STAT = Symbol('stat')\nconst READDIR = Symbol('readdir')\nconst ONREADDIR = Symbol('onreaddir')\nconst PIPE = Symbol('pipe')\nconst ENTRY = Symbol('entry')\nconst ENTRYOPT = Symbol('entryOpt')\nconst WRITEENTRYCLASS = Symbol('writeEntryClass')\nconst WRITE = Symbol('write')\nconst ONDRAIN = Symbol('ondrain')\n\nimport path from 'path'\nimport { normalizeWindowsPath } from './normalize-windows-path.js'\nimport { TarOptions } from './options.js'\n\nexport class Pack\n  extends Minipass<Buffer, ReadEntry | string, WarnEvent<Buffer>>\n  implements Warner\n{\n  opt: TarOptions\n  cwd: string\n  maxReadSize?: number\n  preservePaths: boolean\n  strict: boolean\n  noPax: boolean\n  prefix: string\n  linkCache: Exclude<TarOptions['linkCache'], undefined>\n  statCache: Exclude<TarOptions['statCache'], undefined>\n  file: string\n  portable: boolean\n  zip?: zlib.BrotliCompress | zlib.Gzip | zlib.ZstdCompress\n  readdirCache: Exclude<TarOptions['readdirCache'], undefined>\n  noDirRecurse: boolean\n  follow: boolean\n  noMtime: boolean\n  mtime?: Date\n  filter: Exclude<TarOptions['filter'], undefined>\n  jobs: number;\n\n  [WRITEENTRYCLASS]: typeof WriteEntry | typeof WriteEntrySync\n  onWriteEntry?: (entry: WriteEntry) => void;\n  // Note: we actually DO need a linked list here, because we\n  // shift() to update the head of the list where we start, but still\n  // while that happens, need to know what the next item in the queue\n  // will be. Since we do multiple jobs in parallel, it's not as simple\n  // as just an Array.shift(), since that would lose the information about\n  // the next job in the list. We could add a .next field on the PackJob\n  // class, but then we'd have to be tracking the tail of the queue the\n  // whole time, and Yallist just does that for us anyway.\n  [QUEUE]: Yallist<PackJob>;\n  [JOBS]: number = 0;\n  [PROCESSING]: boolean = false;\n  [ENDED]: boolean = false\n\n  constructor(opt: TarOptions = {}) {\n    //@ts-ignore\n    super()\n    this.opt = opt\n    this.file = opt.file || ''\n    this.cwd = opt.cwd || process.cwd()\n    this.maxReadSize = opt.maxReadSize\n    this.preservePaths = !!opt.preservePaths\n    this.strict = !!opt.strict\n    this.noPax = !!opt.noPax\n    this.prefix = normalizeWindowsPath(opt.prefix || '')\n    this.linkCache = opt.linkCache || new Map()\n    this.statCache = opt.statCache || new Map()\n    this.readdirCache = opt.readdirCache || new Map()\n    this.onWriteEntry = opt.onWriteEntry\n\n    this[WRITEENTRYCLASS] = WriteEntry\n    if (typeof opt.onwarn === 'function') {\n      this.on('warn', opt.onwarn)\n    }\n\n    this.portable = !!opt.portable\n\n    if (opt.gzip || opt.brotli || opt.zstd) {\n      if ((opt.gzip ? 1 : 0) + (opt.brotli ? 1 : 0) + (opt.zstd ? 1 : 0) > 1) {\n        throw new TypeError('gzip, brotli, zstd are mutually exclusive')\n      }\n      if (opt.gzip) {\n        if (typeof opt.gzip !== 'object') {\n          opt.gzip = {}\n        }\n        if (this.portable) {\n          opt.gzip.portable = true\n        }\n        this.zip = new zlib.Gzip(opt.gzip)\n      }\n      if (opt.brotli) {\n        if (typeof opt.brotli !== 'object') {\n          opt.brotli = {}\n        }\n        this.zip = new zlib.BrotliCompress(opt.brotli)\n      }\n      if (opt.zstd) {\n        if (typeof opt.zstd !== 'object') {\n          opt.zstd = {}\n        }\n        this.zip = new zlib.ZstdCompress(opt.zstd)\n      }\n      /* c8 ignore next */\n      if (!this.zip) throw new Error('impossible')\n      const zip = this.zip\n      zip.on('data', chunk => super.write(chunk as unknown as string))\n      zip.on('end', () => super.end())\n      zip.on('drain', () => this[ONDRAIN]())\n      this.on('resume', () => zip.resume())\n    } else {\n      this.on('drain', this[ONDRAIN])\n    }\n\n    this.noDirRecurse = !!opt.noDirRecurse\n    this.follow = !!opt.follow\n    this.noMtime = !!opt.noMtime\n    if (opt.mtime) this.mtime = opt.mtime\n\n    this.filter =\n      typeof opt.filter === 'function' ? opt.filter : () => true\n\n    this[QUEUE] = new Yallist<PackJob>()\n    this[JOBS] = 0\n    this.jobs = Number(opt.jobs) || 4\n    this[PROCESSING] = false\n    this[ENDED] = false\n  }\n\n  [WRITE](chunk: Buffer) {\n    return super.write(chunk as unknown as string)\n  }\n\n  add(path: string | ReadEntry) {\n    this.write(path)\n    return this\n  }\n\n  end(cb?: () => void): this\n  end(path: string | ReadEntry, cb?: () => void): this\n  end(\n    path: string | ReadEntry,\n    encoding?: Minipass.Encoding,\n    cb?: () => void,\n  ): this\n  end(\n    path?: string | ReadEntry | (() => void),\n    encoding?: Minipass.Encoding | (() => void),\n    cb?: () => void,\n  ) {\n    /* c8 ignore start */\n    if (typeof path === 'function') {\n      cb = path\n      path = undefined\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n      encoding = undefined\n    }\n    /* c8 ignore stop */\n    if (path) {\n      this.add(path)\n    }\n    this[ENDED] = true\n    this[PROCESS]()\n    /* c8 ignore next */\n    if (cb) cb()\n    return this\n  }\n\n  write(path: string | ReadEntry) {\n    if (this[ENDED]) {\n      throw new Error('write after end')\n    }\n\n    if (path instanceof ReadEntry) {\n      this[ADDTARENTRY](path)\n    } else {\n      this[ADDFSENTRY](path)\n    }\n    return this.flowing\n  }\n\n  [ADDTARENTRY](p: ReadEntry) {\n    const absolute = normalizeWindowsPath(\n      path.resolve(this.cwd, p.path),\n    )\n    // in this case, we don't have to wait for the stat\n    if (!this.filter(p.path, p)) {\n      p.resume()\n    } else {\n      const job = new PackJob(p.path, absolute)\n      job.entry = new WriteEntryTar(p, this[ENTRYOPT](job))\n      job.entry.on('end', () => this[JOBDONE](job))\n      this[JOBS] += 1\n      this[QUEUE].push(job)\n    }\n\n    this[PROCESS]()\n  }\n\n  [ADDFSENTRY](p: string) {\n    const absolute = normalizeWindowsPath(path.resolve(this.cwd, p))\n    this[QUEUE].push(new PackJob(p, absolute))\n    this[PROCESS]()\n  }\n\n  [STAT](job: PackJob) {\n    job.pending = true\n    this[JOBS] += 1\n    const stat = this.follow ? 'stat' : 'lstat'\n    fs[stat](job.absolute, (er, stat) => {\n      job.pending = false\n      this[JOBS] -= 1\n      if (er) {\n        this.emit('error', er)\n      } else {\n        this[ONSTAT](job, stat)\n      }\n    })\n  }\n\n  [ONSTAT](job: PackJob, stat: Stats) {\n    this.statCache.set(job.absolute, stat)\n    job.stat = stat\n\n    // now we have the stat, we can filter it.\n    if (!this.filter(job.path, stat)) {\n      job.ignore = true\n    }\n\n    this[PROCESS]()\n  }\n\n  [READDIR](job: PackJob) {\n    job.pending = true\n    this[JOBS] += 1\n    fs.readdir(job.absolute, (er, entries) => {\n      job.pending = false\n      this[JOBS] -= 1\n      if (er) {\n        return this.emit('error', er)\n      }\n      this[ONREADDIR](job, entries)\n    })\n  }\n\n  [ONREADDIR](job: PackJob, entries: string[]) {\n    this.readdirCache.set(job.absolute, entries)\n    job.readdir = entries\n    this[PROCESS]()\n  }\n\n  [PROCESS]() {\n    if (this[PROCESSING]) {\n      return\n    }\n\n    this[PROCESSING] = true\n    for (\n      let w = this[QUEUE].head;\n      !!w && this[JOBS] < this.jobs;\n      w = w.next\n    ) {\n      this[PROCESSJOB](w.value)\n      if (w.value.ignore) {\n        const p = w.next\n        this[QUEUE].removeNode(w)\n        w.next = p\n      }\n    }\n\n    this[PROCESSING] = false\n\n    if (this[ENDED] && !this[QUEUE].length && this[JOBS] === 0) {\n      if (this.zip) {\n        this.zip.end(EOF)\n      } else {\n        super.write(EOF as unknown as string)\n        super.end()\n      }\n    }\n  }\n\n  get [CURRENT]() {\n    return this[QUEUE] && this[QUEUE].head && this[QUEUE].head.value\n  }\n\n  [JOBDONE](_job: PackJob) {\n    this[QUEUE].shift()\n    this[JOBS] -= 1\n    this[PROCESS]()\n  }\n\n  [PROCESSJOB](job: PackJob) {\n    if (job.pending) {\n      return\n    }\n\n    if (job.entry) {\n      if (job === this[CURRENT] && !job.piped) {\n        this[PIPE](job)\n      }\n      return\n    }\n\n    if (!job.stat) {\n      const sc = this.statCache.get(job.absolute)\n      if (sc) {\n        this[ONSTAT](job, sc)\n      } else {\n        this[STAT](job)\n      }\n    }\n    if (!job.stat) {\n      return\n    }\n\n    // filtered out!\n    if (job.ignore) {\n      return\n    }\n\n    if (\n      !this.noDirRecurse &&\n      job.stat.isDirectory() &&\n      !job.readdir\n    ) {\n      const rc = this.readdirCache.get(job.absolute)\n      if (rc) {\n        this[ONREADDIR](job, rc)\n      } else {\n        this[READDIR](job)\n      }\n      if (!job.readdir) {\n        return\n      }\n    }\n\n    // we know it doesn't have an entry, because that got checked above\n    job.entry = this[ENTRY](job)\n    if (!job.entry) {\n      job.ignore = true\n      return\n    }\n\n    if (job === this[CURRENT] && !job.piped) {\n      this[PIPE](job)\n    }\n  }\n\n  [ENTRYOPT](job: PackJob): TarOptions {\n    return {\n      onwarn: (code, msg, data) => this.warn(code, msg, data),\n      noPax: this.noPax,\n      cwd: this.cwd,\n      absolute: job.absolute,\n      preservePaths: this.preservePaths,\n      maxReadSize: this.maxReadSize,\n      strict: this.strict,\n      portable: this.portable,\n      linkCache: this.linkCache,\n      statCache: this.statCache,\n      noMtime: this.noMtime,\n      mtime: this.mtime,\n      prefix: this.prefix,\n      onWriteEntry: this.onWriteEntry,\n    }\n  }\n\n  [ENTRY](job: PackJob) {\n    this[JOBS] += 1\n    try {\n      const e = new this[WRITEENTRYCLASS](\n        job.path,\n        this[ENTRYOPT](job),\n      )\n      return e\n        .on('end', () => this[JOBDONE](job))\n        .on('error', er => this.emit('error', er))\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n\n  [ONDRAIN]() {\n    if (this[CURRENT] && this[CURRENT].entry) {\n      this[CURRENT].entry.resume()\n    }\n  }\n\n  // like .pipe() but using super, because our write() is special\n  [PIPE](job: PackJob) {\n    job.piped = true\n\n    if (job.readdir) {\n      job.readdir.forEach(entry => {\n        const p = job.path\n        const base = p === './' ? '' : p.replace(/\\/*$/, '/')\n        this[ADDFSENTRY](base + entry)\n      })\n    }\n\n    const source = job.entry\n    const zip = this.zip\n    /* c8 ignore start */\n    if (!source) throw new Error('cannot pipe without source')\n    /* c8 ignore stop */\n\n    if (zip) {\n      source.on('data', chunk => {\n        if (!zip.write(chunk)) {\n          source.pause()\n        }\n      })\n    } else {\n      source.on('data', chunk => {\n        if (!super.write(chunk as unknown as string)) {\n          source.pause()\n        }\n      })\n    }\n  }\n\n  pause() {\n    if (this.zip) {\n      this.zip.pause()\n    }\n    return super.pause()\n  }\n  warn(\n    code: string,\n    message: string | Error,\n    data: WarnData = {},\n  ): void {\n    warnMethod(this, code, message, data)\n  }\n}\n\nexport class PackSync extends Pack {\n  sync: true = true\n  constructor(opt: TarOptions) {\n    super(opt)\n    this[WRITEENTRYCLASS] = WriteEntrySync\n  }\n\n  // pause/resume are no-ops in sync streams.\n  pause() {}\n  resume() {}\n\n  [STAT](job: PackJob) {\n    const stat = this.follow ? 'statSync' : 'lstatSync'\n    this[ONSTAT](job, fs[stat](job.absolute))\n  }\n\n  [READDIR](job: PackJob) {\n    this[ONREADDIR](job, fs.readdirSync(job.absolute))\n  }\n\n  // gotta get it all in this tick\n  [PIPE](job: PackJob) {\n    const source = job.entry\n    const zip = this.zip\n\n    if (job.readdir) {\n      job.readdir.forEach(entry => {\n        const p = job.path\n        const base = p === './' ? '' : p.replace(/\\/*$/, '/')\n        this[ADDFSENTRY](base + entry)\n      })\n    }\n\n    /* c8 ignore start */\n    if (!source) throw new Error('Cannot pipe without source')\n    /* c8 ignore stop */\n\n    if (zip) {\n      source.on('data', chunk => {\n        zip.write(chunk)\n      })\n    } else {\n      source.on('data', chunk => {\n        super[WRITE](chunk)\n      })\n    }\n  }\n}\n"]}